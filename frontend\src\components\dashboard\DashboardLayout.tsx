'use client'

import React, { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { DashboardSidebar } from './DashboardSidebar'
import { Button } from '@/components/ui/Button'
import { UserDropdown } from '@/components/ui/UserDropdown'

interface DashboardLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
  className?: string
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  title,
  subtitle,
  actions,
  className = ''
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user, logout } = useAuth()
  const router = useRouter()

  const handleLogout = () => {
    logout()
    router.push('/')
  }

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  return (
    <div className="min-h-screen bg-slate-50 lg:flex lg:h-auto">
      {/* Sidebar */}
      <DashboardSidebar isOpen={sidebarOpen} onToggle={toggleSidebar} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Top Header */}
        <header className="bg-white border-b border-slate-200 px-4 py-[10px] lg:px-6 lg:py-[10px] flex-shrink-0">
          <div className="flex items-center justify-between">
            {/* Left side - Mobile menu button and title */}
            <div className="flex items-center space-x-4">
              {/* Mobile menu button */}
              <button
                type="button"
                onClick={toggleSidebar}
                className="lg:hidden p-2 rounded-lg text-slate-600 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                aria-label="Toggle sidebar"
                aria-expanded={sidebarOpen}
                aria-controls="dashboard-sidebar"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>

              {/* Page title */}
              <div>
                {title && <h1 className="text-xl lg:text-2xl font-bold text-slate-900">{title}</h1>}
                {subtitle && <p className="text-sm text-slate-600">{subtitle}</p>}
              </div>
            </div>

            {/* Right side - Actions and user menu */}
            <div className="flex items-center space-x-4">
              {/* Page actions */}
              {actions && <div className="hidden sm:flex items-center space-x-2">{actions}</div>}

              {/* Notifications */}
              <button
                className="p-2 rounded-lg text-slate-600 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 relative"
                aria-label="View notifications"
                aria-describedby="notification-count"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12"
                  />
                </svg>
                {/* Notification badge */}
                <span
                  id="notification-count"
                  className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
                  aria-label="3 unread notifications"
                ></span>
              </button>

              {/* User menu */}
              {user && <UserDropdown user={user} onLogout={handleLogout} />}

              {/* Logout button - mobile only */}
              <Button onClick={handleLogout} variant="outline" size="sm" className="sm:hidden">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
              </Button>
            </div>
          </div>
          {/* Mobile actions */}
          {actions && <div className="sm:hidden mt-4 flex items-center space-x-2">{actions}</div>}
        </header>

        {/* Page Content */}
        <main
          className={`flex-1 pt-4 pb-0 lg:px-6 lg:pt-4 lg:pb-8 ${className}`}
          role="main"
          aria-label="Dashboard content"
        >
          {children}
        </main>
      </div>
    </div>
  )
}
